import pandas as pd
import os

# Create test data with headers
data1 = {
    'Header1': ['A', 'B', 'C'],
    'Header2': ['1', '2', '3'], 
    'Header3': ['SN001', 'SN002', 'SN003'],
    'Header4': ['IMEI001', 'IMEI002', 'IMEI003'],
    'Header5': ['HW001', 'HW002', 'HW003']
}

data2 = {
    'Header1': ['X', 'Y', 'Z'],
    'Header2': ['10', '20', '30'],
    'Header3': ['IMEI001', 'IMEI002', 'IMEI004'],  # Different IMEI for testing
    'Header4': ['SN001', 'SN002', 'SN003'],
    'Header5': ['HW001', 'HW002', 'HW003']
}

df1 = pd.DataFrame(data1)
df2 = pd.DataFrame(data2)

# Create Excel file with two sheets
with pd.ExcelWriter('test_header_file.xlsx', engine='openpyxl') as writer:
    df1.to_excel(writer, sheet_name='Sheet1', index=False)
    df2.to_excel(writer, sheet_name='Sheet2', index=False)

print("Test Excel file created with headers")

# Test the fixed logic
file_path = 'test_header_file.xlsx'

# Settings from the config file
sheet1_sn_col = 2  # 0-based index (3rd column)
sheet1_imei_col = 3  # 0-based index (4th column)
sheet2_sn_col = 3  # 0-based index (4th column)
sheet2_imei_col = 2  # 0-based index (3rd column)

def strip_whitespace(value):
    """Strip whitespace from both ends of a string value."""
    if isinstance(value, str):
        return value.strip()
    return value

try:
    # Read both sheets with header=None (as in the fixed code)
    df1 = pd.read_excel(file_path, sheet_name=0, engine='openpyxl', header=None, dtype=str)
    df2 = pd.read_excel(file_path, sheet_name=1, engine='openpyxl', header=None, dtype=str)
    
    print("\nOriginal df1 (with header as first row):")
    print(df1.head())
    print("\nOriginal df2 (with header as first row):")
    print(df2.head())
    
    # Skip the header row (first row) since we're using header=None
    df1 = df1.iloc[1:]  # Skip first row (header)
    df2 = df2.iloc[1:]  # Skip first row (header)
    
    # Reset index after dropping the header row
    df1 = df1.reset_index(drop=True)
    df2 = df2.reset_index(drop=True)
    
    print("\nAfter skipping header row df1:")
    print(df1)
    print("\nAfter skipping header row df2:")
    print(df2)
    
    # Convert all columns to string
    df1 = df1.astype(str)
    df2 = df2.astype(str)
    
    # Get total rows
    total_rows = len(df1)
    
    # Manually set column names for the first sheet
    df1.columns = ['Elemek száma:', str(total_rows), 'SN', 'IMEI_1'] + list(df1.columns[4:])
    
    # Manually set column names for the second sheet
    df2_column_names = [f'Col_{i}' for i in range(len(df2.columns))]
    df2.columns = df2_column_names
    
    print(f"\nAfter renaming df1 columns: {df1.columns.tolist()}")
    print(f"After renaming df2 columns: {df2.columns.tolist()}")
    
    # Get column names based on settings
    col_df1_sn = df1.columns[sheet1_sn_col]
    col_df1_imei = df1.columns[sheet1_imei_col]
    col_df2_sn = df2.columns[sheet2_sn_col]
    col_df2_imei = df2.columns[sheet2_imei_col]
    
    print(f"\ndf1 SN column: {col_df1_sn}")
    print(f"df1 IMEI column: {col_df1_imei}")
    print(f"df2 SN column: {col_df2_sn}")
    print(f"df2 IMEI column: {col_df2_imei}")
    
    # Apply whitespace stripping
    df1[col_df1_sn] = df1[col_df1_sn].astype(str).apply(strip_whitespace)
    df1[col_df1_imei] = df1[col_df1_imei].astype(str).apply(strip_whitespace)
    df2[col_df2_sn] = df2[col_df2_sn].astype(str).apply(strip_whitespace)
    df2[col_df2_imei] = df2[col_df2_imei].astype(str).apply(strip_whitespace)
    
    # Rename columns to standard names
    df1.rename(columns={col_df1_sn: 'SN', col_df1_imei: 'IMEI_df1'}, inplace=True)
    df2.rename(columns={col_df2_sn: 'SN', col_df2_imei: 'IMEI_df2'}, inplace=True)
    
    print("\nFinal df1 data:")
    print(df1)
    print("\nFinal df2 data:")
    print(df2)
    
    # Merge the dataframes
    merged_df = pd.merge(
        df1,
        df2,
        on='SN',
        how='left',
        suffixes=('_df1', '_df2')
    )
    
    print("\nMerged data:")
    print(merged_df)
    
    # Check if IMEI_df2 column exists and has valid data
    if 'IMEI_df2' in merged_df.columns:
        print("\n✅ IMEI_df2 column exists!")
        print("IMEI_df2 values:", merged_df['IMEI_df2'].tolist())
        
        # Check for NaN values
        nan_count = merged_df['IMEI_df2'].isna().sum()
        print(f"Number of NaN values in IMEI_df2: {nan_count}")
        
        # Check for 'nan' string values
        nan_string_count = (merged_df['IMEI_df2'] == 'nan').sum()
        print(f"Number of 'nan' string values in IMEI_df2: {nan_string_count}")
    else:
        print("❌ IMEI_df2 column does NOT exist!")
        print("Available columns:", merged_df.columns.tolist())

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

# Clean up
if os.path.exists('test_header_file.xlsx'):
    os.remove('test_header_file.xlsx')
