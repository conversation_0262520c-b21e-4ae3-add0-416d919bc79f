import pandas as pd
import os

# Test the fixed code logic
file_path = 'test_file.xlsx'

# Settings from the config file
sheet1_sn_col = 2  # 0-based index (3rd column)
sheet1_imei_col = 3  # 0-based index (4th column)
sheet2_sn_col = 3  # 0-based index (4th column)
sheet2_imei_col = 2  # 0-based index (3rd column)

def strip_whitespace(value):
    """Strip whitespace from both ends of a string value."""
    if isinstance(value, str):
        return value.strip()
    return value

try:
    # Read both sheets with header=None (as in the fixed code)
    df1 = pd.read_excel(file_path, sheet_name=0, engine='openpyxl', header=None, dtype=str)
    df2 = pd.read_excel(file_path, sheet_name=1, engine='openpyxl', header=None, dtype=str)
    
    print("Original df1 columns:", df1.columns.tolist())
    print("Original df2 columns:", df2.columns.tolist())
    print()
    
    # Convert all columns to string
    df1 = df1.astype(str)
    df2 = df2.astype(str)
    
    # Get total rows
    total_rows = len(df1)
    
    # Manually set column names for the first sheet
    df1.columns = ['Elemek száma:', str(total_rows), 'SN', 'IMEI_1'] + list(df1.columns[4:])
    
    # Manually set column names for the second sheet
    df2_column_names = [f'Col_{i}' for i in range(len(df2.columns))]
    df2.columns = df2_column_names
    
    print("After renaming df1 columns:", df1.columns.tolist())
    print("After renaming df2 columns:", df2.columns.tolist())
    print()
    
    # Get column names based on settings
    col_df1_sn = df1.columns[sheet1_sn_col]
    col_df1_imei = df1.columns[sheet1_imei_col]
    col_df2_sn = df2.columns[sheet2_sn_col]
    col_df2_imei = df2.columns[sheet2_imei_col]
    
    print(f"df1 SN column: {col_df1_sn}")
    print(f"df1 IMEI column: {col_df1_imei}")
    print(f"df2 SN column: {col_df2_sn}")
    print(f"df2 IMEI column: {col_df2_imei}")
    print()
    
    # Apply whitespace stripping
    df1[col_df1_sn] = df1[col_df1_sn].astype(str).apply(strip_whitespace)
    df1[col_df1_imei] = df1[col_df1_imei].astype(str).apply(strip_whitespace)
    df2[col_df2_sn] = df2[col_df2_sn].astype(str).apply(strip_whitespace)
    df2[col_df2_imei] = df2[col_df2_imei].astype(str).apply(strip_whitespace)
    
    # Rename columns to standard names
    df1.rename(columns={col_df1_sn: 'SN', col_df1_imei: 'IMEI_df1'}, inplace=True)
    df2.rename(columns={col_df2_sn: 'SN', col_df2_imei: 'IMEI_df2'}, inplace=True)
    
    print("df1 data:")
    print(df1)
    print()
    print("df2 data:")
    print(df2)
    print()
    
    # Merge the dataframes
    merged_df = pd.merge(
        df1,
        df2,
        on='SN',
        how='left',
        suffixes=('_df1', '_df2')
    )
    
    print("Merged data:")
    print(merged_df)
    print()
    
    # Check if IMEI_df2 column exists and has valid data
    if 'IMEI_df2' in merged_df.columns:
        print("IMEI_df2 column exists!")
        print("IMEI_df2 values:", merged_df['IMEI_df2'].tolist())
        
        # Check for NaN values
        nan_count = merged_df['IMEI_df2'].isna().sum()
        print(f"Number of NaN values in IMEI_df2: {nan_count}")
    else:
        print("IMEI_df2 column does NOT exist!")
        print("Available columns:", merged_df.columns.tolist())

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
