import pandas as pd

# Create realistic test data similar to the screenshot
data1 = {
    '<PERSON><PERSON><PERSON> szá<PERSON>': ['450', '450', '450', '450', '450'],
    'B': ['nan', 'nan', 'nan', 'nan', 'nan'],
    'C': ['019384601', '019384602', '019384603', '019384604', '019384605'],
    'SN': ['351252931', '351252932', '351252933', '351252934', '351252935'],
    'IMEI_df1': ['123456789012345', '123456789012346', '123456789012347', '123456789012348', '123456789012349']
}

data2 = {
    'A': ['X1', 'X2', 'X3', 'X4', 'X5'],
    'B': ['Y1', 'Y2', 'Y3', 'Y4', 'Y5'],
    'IMEI': ['123456789012345', '123456789012346', '123456789012347', '123456789012348', '123456789012349'],
    'HW_ID': ['351252931', '351252932', '351252933', '351252934', '351252935'],
    'E': ['Z1', 'Z2', 'Z3', 'Z4', 'Z5']
}

df1 = pd.DataFrame(data1)
df2 = pd.DataFrame(data2)

print("Creating realistic test file...")
print("Sheet1 data:")
print(df1)
print("\nSheet2 data:")
print(df2)

# Create Excel file with two sheets
with pd.ExcelWriter('realistic_test.xlsx', engine='openpyxl') as writer:
    df1.to_excel(writer, sheet_name='Sheet1', index=False)
    df2.to_excel(writer, sheet_name='Sheet2', index=False)

print("\nRealistic test Excel file created: realistic_test.xlsx")
print("You can now test this with the M2M SIMEI Checker application.")
