import pandas as pd
import os

# Create test data for two sheets
data1 = {
    'Col1': ['A', 'B', 'C'],
    'Col2': ['1', '2', '3'], 
    'Col3': ['SN001', 'SN002', 'SN003'],
    'Col4': ['IMEI001', 'IMEI002', 'IMEI003'],
    'Col5': ['HW001', 'HW002', 'HW003']
}

data2 = {
    'Col1': ['X', 'Y', 'Z'],
    'Col2': ['10', '20', '30'],
    'Col3': ['IMEI001', 'IMEI002', 'IMEI004'],  # Different IMEI for testing
    'Col4': ['SN001', 'SN002', 'SN003'],
    'Col5': ['HW001', 'HW002', 'HW003']
}

df1 = pd.DataFrame(data1)
df2 = pd.DataFrame(data2)

# Create Excel file with two sheets
with pd.ExcelWriter('test_file.xlsx', engine='openpyxl') as writer:
    df1.to_excel(writer, sheet_name='Sheet1', index=False)
    df2.to_excel(writer, sheet_name='Sheet2', index=False)

print("Test Excel file created: test_file.xlsx")
print("Sheet1 columns:", df1.columns.tolist())
print("Sheet2 columns:", df2.columns.tolist())
