Összes elem: 450
IMEI eltérések száma: 0
Oszlop eltérések száma: 1100
Duplikált értékek száma: 0


Oszloponkénti összehasonlítás:

A(z) 'COUNT' oszlop összehasonlítása a(z) '000222' értékkel.
A(z) 'COUNT' oszlopban eltérések találhatók az alábbi sorokban:
Sor 3: el<PERSON><PERSON><PERSON>: '000223'
Sor 4: el<PERSON><PERSON><PERSON>: '000220'
Sor 5: el<PERSON><PERSON><PERSON>rt<PERSON>: '000224'
Sor 6: el<PERSON><PERSON><PERSON>: '000221'
Sor 7: el<PERSON><PERSON><PERSON> ért<PERSON>: '000218'
Sor 8: el<PERSON><PERSON><PERSON>: '000219'
Sor 9: el<PERSON><PERSON><PERSON>rt<PERSON>: '000269'
Sor 10: el<PERSON><PERSON><PERSON> ért<PERSON>: '000267'
Sor 11: el<PERSON><PERSON><PERSON>: '000268'
Sor 12: el<PERSON><PERSON><PERSON>: '000266'
Sor 13: el<PERSON><PERSON><PERSON>: '000265'
Sor 14: <PERSON><PERSON><PERSON><PERSON>: '000264'
Sor 15: el<PERSON><PERSON><PERSON>: '000263'
Sor 16: el<PERSON><PERSON><PERSON> érték: '000262'
Sor 17: eltérő érték: '000261'
Sor 18: eltérő érték: '000260'
Sor 19: eltérő érték: '000259'
Sor 20: eltérő érték: '000258'
Sor 21: eltérő érték: '000257'
Sor 22: eltérő érték: '000225'
Sor 23: eltérő érték: '000226'
Sor 24: eltérő érték: '000450'
Sor 25: eltérő érték: '000448'
Sor 26: eltérő érték: '000216'
Sor 27: eltérő érték: '000217'
Sor 28: eltérő érték: '000214'
Sor 29: eltérő érték: '000215'
Sor 30: eltérő érték: '000212'
Sor 31: eltérő érték: '000213'
Sor 32: eltérő érték: '000210'
Sor 33: eltérő érték: '000211'
Sor 34: eltérő érték: '000208'
Sor 35: eltérő érték: '000209'
Sor 36: eltérő érték: '000207'
Sor 37: eltérő érték: '000205'
Sor 38: eltérő érték: '000206'
Sor 39: eltérő érték: '000433'
Sor 40: eltérő érték: '000434'
Sor 41: eltérő érték: '000435'
Sor 42: eltérő érték: '000436'
Sor 43: eltérő érték: '000437'
Sor 44: eltérő érték: '000438'
Sor 45: eltérő érték: '000440'
Sor 46: eltérő érték: '000439'
Sor 47: eltérő érték: '000441'
Sor 48: eltérő érték: '000442'
Sor 49: eltérő érték: '000443'
Sor 50: eltérő érték: '000444'
Sor 51: eltérő érték: '000447'
Sor 52: eltérő érték: '000067'
Sor 53: eltérő érték: '000069'
Sor 54: eltérő érték: '000068'
Sor 55: eltérő érték: '000065'
Sor 56: eltérő érték: '000066'
Sor 57: eltérő érték: '000445'
Sor 58: eltérő érték: '000446'
Sor 59: eltérő érték: '000064'
Sor 60: eltérő érték: '000063'
Sor 61: eltérő érték: '000059'
Sor 62: eltérő érték: '000062'
Sor 63: eltérő érték: '000061'
Sor 64: eltérő érték: '000060'
Sor 65: eltérő érték: '000057'
Sor 66: eltérő érték: '000058'
Sor 67: eltérő érték: '000056'
Sor 68: eltérő érték: '000055'
Sor 69: eltérő érték: '000054'
Sor 70: eltérő érték: '000053'
Sor 71: eltérő érték: '000016'
Sor 72: eltérő érték: '000015'
Sor 73: eltérő érték: '000014'
Sor 74: eltérő érték: '000013'
Sor 75: eltérő érték: '000011'
Sor 76: eltérő érték: '000012'
Sor 77: eltérő érték: '000010'
Sor 78: eltérő érték: '000009'
Sor 79: eltérő érték: '000008'
Sor 80: eltérő érték: '000005'
Sor 81: eltérő érték: '000004'
Sor 82: eltérő érték: '000003'
Sor 83: eltérő érték: '000002'
Sor 84: eltérő érték: '000017'
Sor 85: eltérő érték: '000395'
Sor 86: eltérő érték: '000396'
Sor 87: eltérő érték: '000007'
Sor 88: eltérő érték: '000006'
Sor 89: eltérő érték: '000018'
Sor 90: eltérő érték: '000397'
Sor 91: eltérő érték: '000394'
Sor 92: eltérő érték: '000393'
Sor 93: eltérő érték: '000392'
Sor 94: eltérő érték: '000432'
Sor 95: eltérő érték: '000431'
Sor 96: eltérő érték: '000430'
Sor 97: eltérő érték: '000411'
Sor 98: eltérő érték: '000410'
Sor 99: eltérő érték: '000409'
Sor 100: eltérő érték: '000408'
Sor 101: eltérő érték: '000391'
Sor 102: eltérő érték: '000390'
Sor 103: eltérő érték: '000389'
Sor 104: eltérő érték: '000388'
Sor 105: eltérő érték: '000387'
Sor 106: eltérő érték: '000386'
Sor 107: eltérő érték: '000385'
Sor 108: eltérő érték: '000406'
Sor 109: eltérő érték: '000407'
Sor 110: eltérő érték: '000405'
Sor 111: eltérő érték: '000404'
Sor 112: eltérő érték: '000403'
Sor 113: eltérő érték: '000402'
Sor 114: eltérő érték: '000401'
Sor 115: eltérő érték: '000384'
Sor 116: eltérő érték: '000383'
Sor 117: eltérő érték: '000382'
Sor 118: eltérő érték: '000381'
Sor 119: eltérő érték: '000416'
Sor 120: eltérő érték: '000418'
Sor 121: eltérő érték: '000419'
Sor 122: eltérő érték: '000400'
Sor 123: eltérő érték: '000398'
Sor 124: eltérő érték: '000081'
Sor 125: eltérő érték: '000080'
Sor 126: eltérő érték: '000082'
Sor 127: eltérő érték: '000399'
Sor 128: eltérő érték: '000079'
Sor 129: eltérő érték: '000417'
Sor 130: eltérő érték: '000420'
Sor 131: eltérő érték: '000421'
Sor 132: eltérő érték: '000422'
Sor 133: eltérő érték: '000425'
Sor 134: eltérő érték: '000426'
Sor 135: eltérő érték: '000427'
Sor 136: eltérő érték: '000086'
Sor 137: eltérő érték: '000085'
Sor 138: eltérő érték: '000084'
Sor 139: eltérő érték: '000083'
Sor 140: eltérő érték: '000078'
Sor 141: eltérő érték: '000077'
Sor 142: eltérő érték: '000076'
Sor 143: eltérő érték: '000423'
Sor 144: eltérő érték: '000424'
Sor 145: eltérő érték: '000429'
Sor 146: eltérő érték: '000428'
Sor 147: eltérő érték: '000378'
Sor 148: eltérő érték: '000379'
Sor 149: eltérő érték: '000380'
Sor 150: eltérő érték: '000075'
Sor 151: eltérő érték: '000074'
Sor 152: eltérő érték: '000310'
Sor 153: eltérő érték: '000312'
Sor 154: eltérő érték: '000314'
Sor 155: eltérő érték: '000316'
Sor 156: eltérő érték: '000317'
Sor 157: eltérő érték: '000311'
Sor 158: eltérő érték: '000313'
Sor 159: eltérő érték: '000315'
Sor 160: eltérő érték: '000309'
Sor 161: eltérő érték: '000320'
Sor 162: eltérő érték: '000319'
Sor 163: eltérő érték: '000321'
Sor 164: eltérő érték: '000322'
Sor 165: eltérő érték: '000035'
Sor 166: eltérő érték: '000318'
Sor 167: eltérő érték: '000033'
Sor 168: eltérő érték: '000034'
Sor 169: eltérő érték: '000031'
Sor 170: eltérő érték: '000029'
Sor 171: eltérő érték: '000030'
Sor 172: eltérő érték: '000324'
Sor 173: eltérő érték: '000325'
Sor 174: eltérő érték: '000323'
Sor 175: eltérő érték: '000023'
Sor 176: eltérő érték: '000304'
Sor 177: eltérő érték: '000326'
Sor 178: eltérő érték: '000305'
Sor 179: eltérő érték: '000298'
Sor 180: eltérő érték: '000020'
Sor 181: eltérő érték: '000022'
Sor 182: eltérő érték: '000302'
Sor 183: eltérő érték: '000306'
Sor 184: eltérő érték: '000303'
Sor 185: eltérő érték: '000307'
Sor 186: eltérő érték: '000308'
Sor 187: eltérő érték: '000299'
Sor 188: eltérő érték: '000300'
Sor 189: eltérő érték: '000301'
Sor 190: eltérő érték: '000028'
Sor 191: eltérő érték: '000025'
Sor 192: eltérő érték: '000027'
Sor 193: eltérő érték: '000026'
Sor 194: eltérő érték: '000024'
Sor 195: eltérő érték: '000019'
Sor 196: eltérő érték: '000021'
Sor 197: eltérő érték: '000240'
Sor 198: eltérő érték: '000241'
Sor 199: eltérő érték: '000242'
Sor 200: eltérő érték: '000284'
Sor 201: eltérő érték: '000281'
Sor 202: eltérő érték: '000282'
Sor 203: eltérő érték: '000280'
Sor 204: eltérő érték: '000279'
Sor 205: eltérő érték: '000277'
Sor 206: eltérő érték: '000278'
Sor 207: eltérő érték: '000275'
Sor 208: eltérő érték: '000276'
Sor 209: eltérő érték: '000127'
Sor 210: eltérő érték: '000126'
Sor 211: eltérő érték: '000274'
Sor 212: eltérő érték: '000125'
Sor 213: eltérő érték: '000123'
Sor 214: eltérő érték: '000128'
Sor 215: eltérő érték: '000131'
Sor 216: eltérő érték: '000130'
Sor 217: eltérő érték: '000129'
Sor 218: eltérő érték: '000134'
Sor 219: eltérő érték: '000133'
Sor 220: eltérő érték: '000132'
Sor 221: eltérő érték: '000135'
Sor 222: eltérő érték: '000412'
Sor 223: eltérő érték: '000136'
Sor 224: eltérő érték: '000143'
Sor 225: eltérő érték: '000141'
Sor 226: eltérő érték: '000142'
Sor 227: eltérő érték: '000415'
Sor 228: eltérő érték: '000414'
Sor 229: eltérő érték: '000413'
Sor 230: eltérő érték: '000144'
Sor 231: eltérő érték: '000146'
Sor 232: eltérő érték: '000145'
Sor 233: eltérő érték: '000147'
Sor 234: eltérő érték: '000148'
Sor 235: eltérő érték: '000243'
Sor 236: eltérő érték: '000244'
Sor 237: eltérő érték: '000245'
Sor 238: eltérő érték: '000246'
Sor 239: eltérő érték: '000247'
Sor 240: eltérő érték: '000297'
Sor 241: eltérő érték: '000296'
Sor 242: eltérő érték: '000295'
Sor 243: eltérő érték: '000292'
Sor 244: eltérő érték: '000293'
Sor 245: eltérő érték: '000294'
Sor 246: eltérő érték: '000291'
Sor 247: eltérő érték: '000150'
Sor 248: eltérő érték: '000149'
Sor 249: eltérő érték: '000152'
Sor 250: eltérő érték: '000151'
Sor 251: eltérő érték: '000153'
Sor 252: eltérő érték: '000121'
Sor 253: eltérő érték: '000120'
Sor 254: eltérő érték: '000339'
Sor 255: eltérő érték: '000337'
Sor 256: eltérő érték: '000336'
Sor 257: eltérő érték: '000335'
Sor 258: eltérő érték: '000334'
Sor 259: eltérő érték: '000333'
Sor 260: eltérő érték: '000332'
Sor 261: eltérő érték: '000331'
Sor 262: eltérő érték: '000330'
Sor 263: eltérő érték: '000329'
Sor 264: eltérő érték: '000328'
Sor 265: eltérő érték: '000327'
Sor 266: eltérő érték: '000449'
Sor 267: eltérő érték: '000377'
Sor 268: eltérő érték: '000376'
Sor 269: eltérő érték: '000375'
Sor 270: eltérő érték: '000374'
Sor 271: eltérő érték: '000345'
Sor 272: eltérő érték: '000373'
Sor 273: eltérő érték: '000372'
Sor 274: eltérő érték: '000371'
Sor 275: eltérő érték: '000370'
Sor 276: eltérő érték: '000348'
Sor 277: eltérő érték: '000366'
Sor 278: eltérő érték: '000369'
Sor 279: eltérő érték: '000368'
Sor 280: eltérő érték: '000367'
Sor 281: eltérő érték: '000346'
Sor 282: eltérő érték: '000248'
Sor 283: eltérő érték: '000249'
Sor 284: eltérő érték: '000250'
Sor 285: eltérő érték: '000251'
Sor 286: eltérő érték: '000252'
Sor 287: eltérő érték: '000290'
Sor 288: eltérő érték: '000289'
Sor 289: eltérő érték: '000288'
Sor 290: eltérő érték: '000283'
Sor 291: eltérő érték: '000285'
Sor 292: eltérő érték: '000286'
Sor 293: eltérő érték: '000287'
Sor 294: eltérő érték: '000343'
Sor 295: eltérő érték: '000124'
Sor 296: eltérő érték: '000341'
Sor 297: eltérő érték: '000342'
Sor 298: eltérő érték: '000122'
Sor 299: eltérő érték: '000340'
Sor 300: eltérő érték: '000338'
Sor 301: eltérő érték: '000254'
Sor 302: eltérő érték: '000104'
Sor 303: eltérő érték: '000051'
Sor 304: eltérő érték: '000052'
Sor 305: eltérő érték: '000255'
Sor 306: eltérő érték: '000045'
Sor 307: eltérő érték: '000138'
Sor 308: eltérő érték: '000137'
Sor 309: eltérő érték: '000139'
Sor 310: eltérő érték: '000140'
Sor 311: eltérő érték: '000256'
Sor 312: eltérő érték: '000253'
Sor 313: eltérő érték: '000050'
Sor 314: eltérő érték: '000049'
Sor 315: eltérő érték: '000048'
Sor 316: eltérő érték: '000047'
Sor 317: eltérő érték: '000044'
Sor 318: eltérő érték: '000043'
Sor 319: eltérő érték: '000036'
Sor 320: eltérő érték: '000232'
Sor 321: eltérő érték: '000239'
Sor 322: eltérő érték: '000353'
Sor 323: eltérő érték: '000046'
Sor 324: eltérő érték: '000042'
Sor 325: eltérő érték: '000041'
Sor 326: eltérő érték: '000040'
Sor 327: eltérő érték: '000039'
Sor 328: eltérő érték: '000037'
Sor 329: eltérő érték: '000038'
Sor 330: eltérő érték: '000227'
Sor 331: eltérő érték: '000228'
Sor 332: eltérő érték: '000229'
Sor 333: eltérő érték: '000230'
Sor 334: eltérő érték: '000231'
Sor 335: eltérő érték: '000233'
Sor 336: eltérő érték: '000234'
Sor 337: eltérő érték: '000235'
Sor 338: eltérő érték: '000236'
Sor 339: eltérő érték: '000237'
Sor 340: eltérő érték: '000238'
Sor 341: eltérő érték: '000347'
Sor 342: eltérő érték: '000365'
Sor 343: eltérő érték: '000349'
Sor 344: eltérő érték: '000364'
Sor 345: eltérő érték: '000350'
Sor 346: eltérő érték: '000363'
Sor 347: eltérő érték: '000362'
Sor 348: eltérő érték: '000352'
Sor 349: eltérő érték: '000272'
Sor 350: eltérő érték: '000273'
Sor 351: eltérő érték: '000270'
Sor 352: eltérő érték: '000204'
Sor 353: eltérő érték: '000203'
Sor 354: eltérő érték: '000202'
Sor 355: eltérő érték: '000189'
Sor 356: eltérő érték: '000188'
Sor 357: eltérő érték: '000182'
Sor 358: eltérő érték: '000180'
Sor 359: eltérő érték: '000174'
Sor 360: eltérő érték: '000194'
Sor 361: eltérő érték: '000193'
Sor 362: eltérő érték: '000192'
Sor 363: eltérő érték: '000191'
Sor 364: eltérő érték: '000190'
Sor 365: eltérő érték: '000187'
Sor 366: eltérő érték: '000186'
Sor 367: eltérő érték: '000185'
Sor 368: eltérő érték: '000184'
Sor 369: eltérő érték: '000183'
Sor 370: eltérő érték: '000201'
Sor 371: eltérő érték: '000200'
Sor 372: eltérő érték: '000199'
Sor 373: eltérő érték: '000198'
Sor 374: eltérő érték: '000197'
Sor 375: eltérő érték: '000196'
Sor 376: eltérő érték: '000195'
Sor 377: eltérő érték: '000173'
Sor 378: eltérő érték: '000172'
Sor 379: eltérő érték: '000165'
Sor 380: eltérő érték: '000169'
Sor 381: eltérő érték: '000168'
Sor 382: eltérő érték: '000167'
Sor 383: eltérő érték: '000166'
Sor 384: eltérő érték: '000092'
Sor 385: eltérő érték: '000091'
Sor 386: eltérő érték: '000090'
Sor 387: eltérő érték: '000089'
Sor 388: eltérő érték: '000087'
Sor 389: eltérő érték: '000119'
Sor 390: eltérő érték: '000118'
Sor 391: eltérő érték: '000179'
Sor 392: eltérő érték: '000178'
Sor 393: eltérő érték: '000177'
Sor 394: eltérő érték: '000176'
Sor 395: eltérő érték: '000175'
Sor 396: eltérő érték: '000088'
Sor 397: eltérő érték: '000117'
Sor 398: eltérő érték: '000116'
Sor 399: eltérő érték: '000001'
Sor 400: eltérő érték: '000115'
Sor 401: eltérő érték: '000114'
Sor 402: eltérő érték: '000113'
Sor 403: eltérő érték: '000181'
Sor 404: eltérő érték: '000094'
Sor 405: eltérő érték: '000093'
Sor 406: eltérő érték: '000109'
Sor 407: eltérő érték: '000105'
Sor 408: eltérő érték: '000171'
Sor 409: eltérő érték: '000170'
Sor 410: eltérő érték: '000164'
Sor 411: eltérő érték: '000097'
Sor 412: eltérő érték: '000096'
Sor 413: eltérő érték: '000095'
Sor 414: eltérő érték: '000112'
Sor 415: eltérő érték: '000111'
Sor 416: eltérő érték: '000110'
Sor 417: eltérő érték: '000108'
Sor 418: eltérő érték: '000107'
Sor 419: eltérő érték: '000106'
Sor 420: eltérő érték: '000156'
Sor 421: eltérő érték: '000160'
Sor 422: eltérő érték: '000157'
Sor 423: eltérő érték: '000162'
Sor 424: eltérő érték: '000161'
Sor 425: eltérő érték: '000163'
Sor 426: eltérő érték: '000359'
Sor 427: eltérő érték: '000271'
Sor 428: eltérő érték: '000158'
Sor 429: eltérő érték: '000159'
Sor 430: eltérő érték: '000101'
Sor 431: eltérő érték: '000100'
Sor 432: eltérő érték: '000099'
Sor 433: eltérő érték: '000098'
Sor 434: eltérő érték: '000155'
Sor 435: eltérő érték: '000154'
Sor 436: eltérő érték: '000103'
Sor 437: eltérő érték: '000102'
Sor 438: eltérő érték: '000361'
Sor 439: eltérő érték: '000360'
Sor 440: eltérő érték: '000356'
Sor 441: eltérő érték: '000357'
Sor 442: eltérő érték: '000344'
Sor 443: eltérő érték: '000355'
Sor 444: eltérő érték: '000351'
Sor 445: eltérő érték: '000358'
Sor 446: eltérő érték: '000354'
Sor 447: eltérő érték: '000073'
Sor 448: eltérő érték: '000072'
Sor 449: eltérő érték: '000071'
Sor 450: eltérő érték: '000070'
Sor 451: eltérő érték: '000032'
A(z) 'HW_ID' oszlop összehasonlítása a(z) '1S00' értékkel.
A(z) 'HW_ID' oszlopban nincs eltérés.
A(z) 'MCU_BL_NAME' oszlop összehasonlítása a(z) 'WM-E1S STD' értékkel.
A(z) 'MCU_BL_NAME' oszlopban nincs eltérés.
A(z) 'MCU_BL_VERSION' oszlop összehasonlítása a(z) 'v3.2.2' értékkel.
A(z) 'MCU_BL_VERSION' oszlopban nincs eltérés.
A(z) 'MCU_FW_NAME' oszlop összehasonlítása a(z) 'WM-E1S STD' értékkel.
A(z) 'MCU_FW_NAME' oszlopban nincs eltérés.
A(z) 'MCU_FW_VERSION' oszlop összehasonlítása a(z) 'v5.3.20' értékkel.
A(z) 'MCU_FW_VERSION' oszlopban nincs eltérés.
A(z) 'GSM_MODULE_TYPE' oszlop összehasonlítása a(z) 'LE910C1-EUX' értékkel.
A(z) 'GSM_MODULE_TYPE' oszlopban nincs eltérés.
A(z) 'GSM_MODULE_FW_VERSION' oszlop összehasonlítása a(z) '25.30.226' értékkel.
A(z) 'GSM_MODULE_FW_VERSION' oszlopban nincs eltérés.
A(z) 'TEST_SEQ_ID' oszlop összehasonlítása a(z) '01' értékkel.
A(z) 'TEST_SEQ_ID' oszlopban nincs eltérés.
A(z) 'TEST_TOOL_VERSION' oszlop összehasonlítása a(z) 'v5.3.18' értékkel.
A(z) 'TEST_TOOL_VERSION' oszlopban nincs eltérés.
A(z) 'TEST_JIG_FW_VERSION' oszlop összes mezője üres.
A(z) 'TEST_BL_VERSION' oszlop összehasonlítása a(z) 'v3.0.0' értékkel.
A(z) 'TEST_BL_VERSION' oszlopban nincs eltérés.
A(z) 'TEST_FW_VERSION' oszlop összehasonlítása a(z) 'v3.0.0' értékkel.
A(z) 'TEST_FW_VERSION' oszlopban nincs eltérés.
A(z) 'TEST_OPERATOR' oszlop összehasonlítása a(z) 'gergo' értékkel.
A(z) 'TEST_OPERATOR' oszlopban nincs eltérés.
A(z) 'TEST_DATE' oszlop összehasonlítása a(z) '2025-06-03' értékkel.
A(z) 'TEST_DATE' oszlopban eltérések találhatók az alábbi sorokban:
Sor 24: eltérő érték: '2025-06-04'
Sor 25: eltérő érték: '2025-06-04'
Sor 39: eltérő érték: '2025-06-04'
Sor 40: eltérő érték: '2025-06-04'
Sor 41: eltérő érték: '2025-06-04'
Sor 42: eltérő érték: '2025-06-04'
Sor 43: eltérő érték: '2025-06-04'
Sor 44: eltérő érték: '2025-06-04'
Sor 45: eltérő érték: '2025-06-04'
Sor 46: eltérő érték: '2025-06-04'
Sor 47: eltérő érték: '2025-06-04'
Sor 48: eltérő érték: '2025-06-04'
Sor 49: eltérő érték: '2025-06-04'
Sor 50: eltérő érték: '2025-06-04'
Sor 51: eltérő érték: '2025-06-04'
Sor 57: eltérő érték: '2025-06-04'
Sor 58: eltérő érték: '2025-06-04'
Sor 70: eltérő érték: '2025-06-02'
Sor 71: eltérő érték: '2025-06-02'
Sor 72: eltérő érték: '2025-06-02'
Sor 73: eltérő érték: '2025-06-02'
Sor 74: eltérő érték: '2025-06-02'
Sor 75: eltérő érték: '2025-06-02'
Sor 76: eltérő érték: '2025-06-02'
Sor 77: eltérő érték: '2025-06-02'
Sor 78: eltérő érték: '2025-06-02'
Sor 79: eltérő érték: '2025-06-02'
Sor 80: eltérő érték: '2025-06-02'
Sor 81: eltérő érték: '2025-06-02'
Sor 82: eltérő érték: '2025-06-02'
Sor 83: eltérő érték: '2025-06-02'
Sor 84: eltérő érték: '2025-06-02'
Sor 85: eltérő érték: '2025-06-04'
Sor 86: eltérő érték: '2025-06-04'
Sor 87: eltérő érték: '2025-06-02'
Sor 88: eltérő érték: '2025-06-02'
Sor 89: eltérő érték: '2025-06-02'
Sor 90: eltérő érték: '2025-06-04'
Sor 91: eltérő érték: '2025-06-04'
Sor 92: eltérő érték: '2025-06-04'
Sor 93: eltérő érték: '2025-06-04'
Sor 94: eltérő érték: '2025-06-04'
Sor 95: eltérő érték: '2025-06-04'
Sor 96: eltérő érték: '2025-06-04'
Sor 97: eltérő érték: '2025-06-04'
Sor 98: eltérő érték: '2025-06-04'
Sor 99: eltérő érték: '2025-06-04'
Sor 100: eltérő érték: '2025-06-04'
Sor 101: eltérő érték: '2025-06-04'
Sor 102: eltérő érték: '2025-06-04'
Sor 103: eltérő érték: '2025-06-04'
Sor 104: eltérő érték: '2025-06-04'
Sor 105: eltérő érték: '2025-06-04'
Sor 106: eltérő érték: '2025-06-04'
Sor 107: eltérő érték: '2025-06-04'
Sor 108: eltérő érték: '2025-06-04'
Sor 109: eltérő érték: '2025-06-04'
Sor 110: eltérő érték: '2025-06-04'
Sor 111: eltérő érték: '2025-06-04'
Sor 112: eltérő érték: '2025-06-04'
Sor 113: eltérő érték: '2025-06-04'
Sor 114: eltérő érték: '2025-06-04'
Sor 115: eltérő érték: '2025-06-04'
Sor 116: eltérő érték: '2025-06-04'
Sor 117: eltérő érték: '2025-06-04'
Sor 118: eltérő érték: '2025-06-04'
Sor 119: eltérő érték: '2025-06-04'
Sor 120: eltérő érték: '2025-06-04'
Sor 121: eltérő érték: '2025-06-04'
Sor 122: eltérő érték: '2025-06-04'
Sor 123: eltérő érték: '2025-06-04'
Sor 127: eltérő érték: '2025-06-04'
Sor 129: eltérő érték: '2025-06-04'
Sor 130: eltérő érték: '2025-06-04'
Sor 131: eltérő érték: '2025-06-04'
Sor 132: eltérő érték: '2025-06-04'
Sor 133: eltérő érték: '2025-06-04'
Sor 134: eltérő érték: '2025-06-04'
Sor 135: eltérő érték: '2025-06-04'
Sor 143: eltérő érték: '2025-06-04'
Sor 144: eltérő érték: '2025-06-04'
Sor 145: eltérő érték: '2025-06-04'
Sor 146: eltérő érték: '2025-06-04'
Sor 147: eltérő érték: '2025-06-04'
Sor 148: eltérő érték: '2025-06-04'
Sor 149: eltérő érték: '2025-06-04'
Sor 152: eltérő érték: '2025-06-04'
Sor 153: eltérő érték: '2025-06-04'
Sor 154: eltérő érték: '2025-06-04'
Sor 155: eltérő érték: '2025-06-04'
Sor 156: eltérő érték: '2025-06-04'
Sor 157: eltérő érték: '2025-06-04'
Sor 158: eltérő érték: '2025-06-04'
Sor 159: eltérő érték: '2025-06-04'
Sor 160: eltérő érték: '2025-06-04'
Sor 161: eltérő érték: '2025-06-04'
Sor 162: eltérő érték: '2025-06-04'
Sor 163: eltérő érték: '2025-06-04'
Sor 164: eltérő érték: '2025-06-04'
Sor 165: eltérő érték: '2025-06-02'
Sor 166: eltérő érték: '2025-06-04'
Sor 167: eltérő érték: '2025-06-02'
Sor 168: eltérő érték: '2025-06-02'
Sor 169: eltérő érték: '2025-06-02'
Sor 170: eltérő érték: '2025-06-02'
Sor 171: eltérő érték: '2025-06-02'
Sor 172: eltérő érték: '2025-06-04'
Sor 173: eltérő érték: '2025-06-04'
Sor 174: eltérő érték: '2025-06-04'
Sor 175: eltérő érték: '2025-06-02'
Sor 176: eltérő érték: '2025-06-04'
Sor 177: eltérő érték: '2025-06-04'
Sor 178: eltérő érték: '2025-06-04'
Sor 180: eltérő érték: '2025-06-02'
Sor 181: eltérő érték: '2025-06-02'
Sor 182: eltérő érték: '2025-06-04'
Sor 183: eltérő érték: '2025-06-04'
Sor 184: eltérő érték: '2025-06-04'
Sor 185: eltérő érték: '2025-06-04'
Sor 186: eltérő érték: '2025-06-04'
Sor 190: eltérő érték: '2025-06-02'
Sor 191: eltérő érték: '2025-06-02'
Sor 192: eltérő érték: '2025-06-02'
Sor 193: eltérő érték: '2025-06-02'
Sor 194: eltérő érték: '2025-06-02'
Sor 195: eltérő érték: '2025-06-02'
Sor 196: eltérő érték: '2025-06-02'
Sor 222: eltérő érték: '2025-06-04'
Sor 227: eltérő érték: '2025-06-04'
Sor 228: eltérő érték: '2025-06-04'
Sor 229: eltérő érték: '2025-06-04'
Sor 254: eltérő érték: '2025-06-04'
Sor 255: eltérő érték: '2025-06-04'
Sor 256: eltérő érték: '2025-06-04'
Sor 257: eltérő érték: '2025-06-04'
Sor 258: eltérő érték: '2025-06-04'
Sor 259: eltérő érték: '2025-06-04'
Sor 260: eltérő érték: '2025-06-04'
Sor 261: eltérő érték: '2025-06-04'
Sor 262: eltérő érték: '2025-06-04'
Sor 263: eltérő érték: '2025-06-04'
Sor 264: eltérő érték: '2025-06-04'
Sor 265: eltérő érték: '2025-06-04'
Sor 266: eltérő érték: '2025-06-04'
Sor 267: eltérő érték: '2025-06-04'
Sor 268: eltérő érték: '2025-06-04'
Sor 269: eltérő érték: '2025-06-04'
Sor 270: eltérő érték: '2025-06-04'
Sor 271: eltérő érték: '2025-06-04'
Sor 272: eltérő érték: '2025-06-04'
Sor 273: eltérő érték: '2025-06-04'
Sor 274: eltérő érték: '2025-06-04'
Sor 275: eltérő érték: '2025-06-04'
Sor 276: eltérő érték: '2025-06-04'
Sor 277: eltérő érték: '2025-06-04'
Sor 278: eltérő érték: '2025-06-04'
Sor 279: eltérő érték: '2025-06-04'
Sor 280: eltérő érték: '2025-06-04'
Sor 281: eltérő érték: '2025-06-04'
Sor 294: eltérő érték: '2025-06-04'
Sor 296: eltérő érték: '2025-06-04'
Sor 297: eltérő érték: '2025-06-04'
Sor 299: eltérő érték: '2025-06-04'
Sor 300: eltérő érték: '2025-06-04'
Sor 303: eltérő érték: '2025-06-02'
Sor 304: eltérő érték: '2025-06-02'
Sor 306: eltérő érték: '2025-06-02'
Sor 313: eltérő érték: '2025-06-02'
Sor 314: eltérő érték: '2025-06-02'
Sor 315: eltérő érték: '2025-06-02'
Sor 316: eltérő érték: '2025-06-02'
Sor 317: eltérő érték: '2025-06-02'
Sor 318: eltérő érték: '2025-06-02'
Sor 319: eltérő érték: '2025-06-02'
Sor 322: eltérő érték: '2025-06-04'
Sor 323: eltérő érték: '2025-06-02'
Sor 324: eltérő érték: '2025-06-02'
Sor 325: eltérő érték: '2025-06-02'
Sor 326: eltérő érték: '2025-06-02'
Sor 327: eltérő érték: '2025-06-02'
Sor 328: eltérő érték: '2025-06-02'
Sor 329: eltérő érték: '2025-06-02'
Sor 341: eltérő érték: '2025-06-04'
Sor 342: eltérő érték: '2025-06-04'
Sor 343: eltérő érték: '2025-06-04'
Sor 344: eltérő érték: '2025-06-04'
Sor 345: eltérő érték: '2025-06-04'
Sor 346: eltérő érték: '2025-06-04'
Sor 347: eltérő érték: '2025-06-04'
Sor 348: eltérő érték: '2025-06-04'
Sor 399: eltérő érték: '2025-05-27'
Sor 426: eltérő érték: '2025-06-04'
Sor 438: eltérő érték: '2025-06-04'
Sor 439: eltérő érték: '2025-06-04'
Sor 440: eltérő érték: '2025-06-04'
Sor 441: eltérő érték: '2025-06-04'
Sor 442: eltérő érték: '2025-06-04'
Sor 443: eltérő érték: '2025-06-04'
Sor 444: eltérő érték: '2025-06-04'
Sor 445: eltérő érték: '2025-06-04'
Sor 446: eltérő érték: '2025-06-04'
Sor 451: eltérő érték: '2025-06-02'
A(z) 'TEST_TIME' oszlop összehasonlítása a(z) '16-36-26' értékkel.
A(z) 'TEST_TIME' oszlopban eltérések találhatók az alábbi sorokban:
Sor 3: eltérő érték: '16-38-13'
Sor 4: eltérő érték: '16-33-17'
Sor 5: eltérő érték: '16-39-00'
Sor 6: eltérő érték: '16-35-11'
Sor 7: eltérő érték: '16-29-57'
Sor 8: eltérő érték: '16-32-35'
Sor 9: eltérő érték: '17-48-31'
Sor 10: eltérő érték: '17-45-28'
Sor 11: eltérő érték: '17-46-58'
Sor 12: eltérő érték: '17-44-05'
Sor 13: eltérő érték: '17-42-57'
Sor 14: eltérő érték: '17-41-13'
Sor 15: eltérő érték: '17-40-28'
Sor 16: eltérő érték: '17-38-00'
Sor 17: eltérő érték: '17-37-57'
Sor 18: eltérő érték: '17-32-01'
Sor 19: eltérő érték: '17-30-48'
Sor 20: eltérő érték: '17-29-11'
Sor 21: eltérő érték: '17-28-06'
Sor 22: eltérő érték: '16-40-48'
Sor 23: eltérő érték: '16-41-55'
Sor 24: eltérő érték: '13-15-13'
Sor 25: eltérő érték: '13-07-07'
Sor 26: eltérő érték: '15-53-51'
Sor 27: eltérő érték: '16-29-35'
Sor 28: eltérő érték: '15-51-18'
Sor 29: eltérő érték: '15-52-24'
Sor 30: eltérő érték: '15-48-35'
Sor 31: eltérő érték: '15-49-46'
Sor 32: eltérő érték: '15-46-03'
Sor 33: eltérő érték: '15-46-56'
Sor 34: eltérő érték: '15-43-00'
Sor 35: eltérő érték: '15-44-14'
Sor 36: eltérő érték: '15-40-57'
Sor 37: eltérő érték: '15-38-00'
Sor 38: eltérő érték: '15-39-47'
Sor 39: eltérő érték: '11-31-03'
Sor 40: eltérő érték: '11-32-14'
Sor 41: eltérő érték: '11-33-20'
Sor 42: eltérő érték: '11-34-26'
Sor 43: eltérő érték: '11-35-36'
Sor 44: eltérő érték: '11-36-27'
Sor 45: eltérő érték: '11-42-03'
Sor 46: eltérő érték: '11-42-03'
Sor 47: eltérő érték: '11-44-27'
Sor 48: eltérő érték: '11-45-12'
Sor 49: eltérő érték: '11-46-38'
Sor 50: eltérő érték: '11-59-57'
Sor 51: eltérő érték: '13-04-03'
Sor 52: eltérő érték: '08-16-17'
Sor 53: eltérő érték: '08-20-23'
Sor 54: eltérő érték: '08-19-52'
Sor 55: eltérő érték: '08-10-10'
Sor 56: eltérő érték: '08-15-39'
Sor 57: eltérő érték: '12-47-07'
Sor 58: eltérő érték: '12-51-18'
Sor 59: eltérő érték: '08-09-13'
Sor 60: eltérő érték: '07-52-40'
Sor 61: eltérő érték: '07-35-37'
Sor 62: eltérő érték: '07-46-09'
Sor 63: eltérő érték: '07-42-38'
Sor 64: eltérő érték: '07-39-09'
Sor 65: eltérő érték: '07-28-40'
Sor 66: eltérő érték: '07-32-23'
Sor 67: eltérő érték: '07-23-59'
Sor 68: eltérő érték: '07-18-46'
Sor 69: eltérő érték: '07-13-29'
Sor 70: eltérő érték: '16-41-26'
Sor 71: eltérő érték: '13-57-51'
Sor 72: eltérő érték: '13-54-40'
Sor 73: eltérő érték: '13-51-29'
Sor 74: eltérő érték: '13-47-58'
Sor 75: eltérő érték: '13-40-00'
Sor 76: eltérő érték: '13-43-24'
Sor 77: eltérő érték: '13-34-40'
Sor 78: eltérő érték: '13-30-10'
Sor 79: eltérő érték: '13-26-04'
Sor 80: eltérő érték: '13-03-58'
Sor 81: eltérő érték: '12-58-21'
Sor 82: eltérő érték: '12-52-57'
Sor 83: eltérő érték: '10-37-58'
Sor 84: eltérő érték: '14-27-13'
Sor 85: eltérő érték: '10-37-48'
Sor 86: eltérő érték: '10-40-46'
Sor 87: eltérő érték: '13-17-55'
Sor 88: eltérő érték: '13-12-10'
Sor 89: eltérő érték: '14-31-07'
Sor 90: eltérő érték: '10-41-29'
Sor 91: eltérő érték: '10-37-44'
Sor 92: eltérő érték: '10-32-16'
Sor 93: eltérő érték: '10-27-16'
Sor 94: eltérő érték: '11-29-48'
Sor 95: eltérő érték: '11-27-43'
Sor 96: eltérő érték: '11-26-24'
Sor 97: eltérő érték: '10-59-17'
Sor 98: eltérő érték: '10-58-20'
Sor 99: eltérő érték: '10-56-50'
Sor 100: eltérő érték: '10-55-44'
Sor 101: eltérő érték: '10-24-49'
Sor 102: eltérő érték: '10-23-38'
Sor 103: eltérő érték: '10-22-12'
Sor 104: eltérő érték: '10-21-12'
Sor 105: eltérő érték: '10-19-56'
Sor 106: eltérő érték: '10-18-57'
Sor 107: eltérő érték: '10-17-34'
Sor 108: eltérő érték: '10-53-01'
Sor 109: eltérő érték: '10-54-23'
Sor 110: eltérő érték: '10-51-46'
Sor 111: eltérő érték: '10-50-18'
Sor 112: eltérő érték: '10-49-06'
Sor 113: eltérő érték: '10-47-45'
Sor 114: eltérő érték: '10-46-28'
Sor 115: eltérő érték: '10-16-02'
Sor 116: eltérő érték: '10-15-03'
Sor 117: eltérő érték: '10-13-11'
Sor 118: eltérő érték: '10-12-23'
Sor 119: eltérő érték: '11-09-47'
Sor 120: eltérő érték: '11-12-16'
Sor 121: eltérő érték: '11-13-05'
Sor 122: eltérő érték: '10-45-18'
Sor 123: eltérő érték: '10-43-06'
Sor 124: eltérő érték: '09-45-06'
Sor 125: eltérő érték: '09-38-26'
Sor 126: eltérő érték: '09-55-00'
Sor 127: eltérő érték: '10-43-59'
Sor 128: eltérő érték: '09-37-25'
Sor 129: eltérő érték: '11-10-32'
Sor 130: eltérő érték: '11-14-31'
Sor 131: eltérő érték: '11-15-37'
Sor 132: eltérő érték: '11-16-51'
Sor 133: eltérő érték: '11-20-12'
Sor 134: eltérő érték: '11-21-43'
Sor 135: eltérő érték: '11-22-51'
Sor 136: eltérő érték: '10-03-52'
Sor 137: eltérő érték: '10-00-25'
Sor 138: eltérő érték: '09-59-56'
Sor 139: eltérő érték: '09-56-34'
Sor 140: eltérő érték: '09-34-42'
Sor 141: eltérő érték: '09-33-53'
Sor 142: eltérő érték: '09-29-08'
Sor 143: eltérő érték: '11-17-52'
Sor 144: eltérő érték: '11-19-11'
Sor 145: eltérő érték: '11-25-12'
Sor 146: eltérő érték: '11-24-00'
Sor 147: eltérő érték: '10-07-08'
Sor 148: eltérő érték: '10-08-06'
Sor 149: eltérő érték: '10-09-29'
Sor 150: eltérő érték: '09-28-28'
Sor 151: eltérő érték: '08-53-46'
Sor 152: eltérő érték: '07-06-38'
Sor 153: eltérő érték: '07-14-41'
Sor 154: eltérő érték: '07-17-30'
Sor 155: eltérő érték: '07-20-43'
Sor 156: eltérő érték: '07-22-37'
Sor 157: eltérő érték: '07-14-07'
Sor 158: eltérő érték: '07-16-40'
Sor 159: eltérő érték: '07-19-32'
Sor 160: eltérő érték: '07-04-47'
Sor 161: eltérő érték: '07-26-12'
Sor 162: eltérő érték: '07-25-34'
Sor 163: eltérő érték: '07-27-46'
Sor 164: eltérő érték: '07-28-41'
Sor 165: eltérő érték: '15-37-24'
Sor 166: eltérő érték: '07-23-32'
Sor 167: eltérő érték: '15-29-58'
Sor 168: eltérő érték: '15-33-52'
Sor 169: eltérő érték: '15-22-47'
Sor 170: eltérő érték: '15-16-14'
Sor 171: eltérő érték: '15-19-55'
Sor 172: eltérő érték: '07-31-15'
Sor 173: eltérő érték: '07-33-34'
Sor 174: eltérő érték: '07-30-16'
Sor 175: eltérő érték: '14-49-15'
Sor 176: eltérő érték: '06-58-47'
Sor 177: eltérő érték: '07-35-18'
Sor 178: eltérő érték: '06-59-41'
Sor 179: eltérő érték: '18-33-10'
Sor 180: eltérő érték: '14-38-51'
Sor 181: eltérő érték: '14-45-01'
Sor 182: eltérő érték: '06-56-08'
Sor 183: eltérő érték: '07-01-14'
Sor 184: eltérő érték: '06-56-39'
Sor 185: eltérő érték: '07-02-08'
Sor 186: eltérő érték: '07-03-41'
Sor 187: eltérő érték: '18-34-29'
Sor 188: eltérő érték: '18-35-40'
Sor 189: eltérő érték: '18-37-20'
Sor 190: eltérő érték: '15-12-17'
Sor 191: eltérő érték: '15-00-58'
Sor 192: eltérő érték: '15-07-40'
Sor 193: eltérő érték: '15-04-02'
Sor 194: eltérő érték: '14-56-31'
Sor 195: eltérő érték: '14-35-50'
Sor 196: eltérő érték: '14-41-59'
Sor 197: eltérő érték: '17-03-38'
Sor 198: eltérő érték: '17-04-54'
Sor 199: eltérő érték: '17-06-23'
Sor 200: eltérő érték: '18-15-06'
Sor 201: eltérő érték: '18-10-51'
Sor 202: eltérő érték: '18-12-27'
Sor 203: eltérő érték: '18-09-21'
Sor 204: eltérő érték: '18-07-48'
Sor 205: eltérő érték: '18-03-58'
Sor 206: eltérő érték: '18-06-35'
Sor 207: eltérő érték: '17-56-42'
Sor 208: eltérő érték: '18-02-51'
Sor 209: eltérő érték: '13-09-13'
Sor 210: eltérő érték: '13-07-54'
Sor 211: eltérő érték: '17-55-24'
Sor 212: eltérő érték: '13-06-20'
Sor 213: eltérő érték: '13-03-23'
Sor 214: eltérő érték: '13-10-54'
Sor 215: eltérő érték: '13-15-44'
Sor 216: eltérő érték: '13-14-32'
Sor 217: eltérő érték: '13-12-15'
Sor 218: eltérő érték: '13-20-12'
Sor 219: eltérő érték: '13-18-39'
Sor 220: eltérő érték: '13-17-21'
Sor 221: eltérő érték: '13-21-41'
Sor 222: eltérő érték: '11-00-41'
Sor 223: eltérő érték: '13-23-57'
Sor 224: eltérő érték: '13-44-05'
Sor 225: eltérő érték: '13-33-14'
Sor 226: eltérő érték: '13-33-32'
Sor 227: eltérő érték: '11-07-41'
Sor 228: eltérő érték: '11-07-37'
Sor 229: eltérő érték: '11-01-49'
Sor 230: eltérő érték: '13-44-09'
Sor 231: eltérő érték: '13-48-01'
Sor 232: eltérő érték: '13-46-57'
Sor 233: eltérő érték: '13-49-33'
Sor 234: eltérő érték: '13-50-41'
Sor 235: eltérő érték: '17-08-45'
Sor 236: eltérő érték: '17-09-42'
Sor 237: eltérő érték: '17-11-31'
Sor 238: eltérő érték: '17-12-37'
Sor 239: eltérő érték: '17-14-25'
Sor 240: eltérő érték: '18-32-02'
Sor 241: eltérő érték: '18-30-47'
Sor 242: eltérő érték: '18-29-45'
Sor 243: eltérő érték: '18-25-43'
Sor 244: eltérő érték: '18-27-10'
Sor 245: eltérő érték: '18-28-20'
Sor 246: eltérő érték: '18-24-16'
Sor 247: eltérő érték: '13-53-34'
Sor 248: eltérő érték: '13-52-15'
Sor 249: eltérő érték: '13-56-47'
Sor 250: eltérő érték: '13-55-20'
Sor 251: eltérő érték: '13-57-52'
Sor 252: eltérő érték: '12-53-14'
Sor 253: eltérő érték: '12-51-38'
Sor 254: eltérő érték: '08-03-42'
Sor 255: eltérő érték: '08-01-08'
Sor 256: eltérő érték: '08-00-59'
Sor 257: eltérő érték: '07-53-13'
Sor 258: eltérő érték: '07-53-10'
Sor 259: eltérő érték: '07-44-42'
Sor 260: eltérő érték: '07-43-34'
Sor 261: eltérő érték: '07-41-42'
Sor 262: eltérő érték: '07-40-44'
Sor 263: eltérő érték: '07-39-16'
Sor 264: eltérő érték: '07-38-11'
Sor 265: eltérő érték: '07-36-29'
Sor 266: eltérő érték: '13-11-18'
Sor 267: eltérő érték: '10-05-30'
Sor 268: eltérő érték: '10-04-28'
Sor 269: eltérő érték: '10-03-09'
Sor 270: eltérő érték: '10-02-05'
Sor 271: eltérő érték: '09-06-19'
Sor 272: eltérő érték: '10-00-41'
Sor 273: eltérő érték: '09-59-24'
Sor 274: eltérő érték: '09-58-27'
Sor 275: eltérő érték: '09-57-05'
Sor 276: eltérő érték: '09-10-43'
Sor 277: eltérő érték: '09-52-16'
Sor 278: eltérő érték: '09-55-55'
Sor 279: eltérő érték: '09-54-52'
Sor 280: eltérő érték: '09-53-36'
Sor 281: eltérő érték: '09-08-23'
Sor 282: eltérő érték: '17-15-32'
Sor 283: eltérő érték: '17-16-56'
Sor 284: eltérő érték: '17-18-10'
Sor 285: eltérő érték: '17-19-26'
Sor 286: eltérő érték: '17-21-12'
Sor 287: eltérő érték: '18-23-04'
Sor 288: eltérő érték: '18-21-45'
Sor 289: eltérő érték: '18-20-40'
Sor 290: eltérő érték: '18-13-39'
Sor 291: eltérő érték: '18-16-31'
Sor 292: eltérő érték: '18-18-00'
Sor 293: eltérő érték: '18-18-54'
Sor 294: eltérő érték: '08-08-39'
Sor 295: eltérő érték: '13-04-46'
Sor 296: eltérő érték: '08-06-18'
Sor 297: eltérő érték: '08-07-57'
Sor 298: eltérő érték: '12-59-05'
Sor 299: eltérő érték: '08-05-40'
Sor 300: eltérő érték: '08-03-19'
Sor 301: eltérő érték: '17-23-45'
Sor 302: eltérő érték: '11-11-54'
Sor 303: eltérő érték: '16-35-35'
Sor 304: eltérő érték: '16-38-14'
Sor 305: eltérő érték: '17-25-00'
Sor 306: eltérő érték: '16-14-29'
Sor 307: eltérő érték: '13-27-07'
Sor 308: eltérő érték: '13-24-50'
Sor 309: eltérő érték: '13-28-54'
Sor 310: eltérő érték: '13-30-11'
Sor 311: eltérő érték: '17-26-20'
Sor 312: eltérő érték: '17-22-10'
Sor 313: eltérő érték: '16-32-35'
Sor 314: eltérő érték: '16-29-44'
Sor 315: eltérő érték: '16-23-49'
Sor 316: eltérő érték: '16-20-53'
Sor 317: eltérő érték: '16-11-19'
Sor 318: eltérő érték: '16-08-25'
Sor 319: eltérő érték: '15-41-04'
Sor 320: eltérő érték: '16-52-40'
Sor 321: eltérő érték: '17-02-08'
Sor 322: eltérő érték: '09-17-36'
Sor 323: eltérő érték: '16-17-50'
Sor 324: eltérő érték: '16-04-55'
Sor 325: eltérő érték: '16-01-20'
Sor 326: eltérő érték: '15-58-14'
Sor 327: eltérő érték: '15-55-02'
Sor 328: eltérő érték: '15-44-32'
Sor 329: eltérő érték: '15-50-46'
Sor 330: eltérő érték: '16-43-53'
Sor 331: eltérő érték: '16-44-55'
Sor 332: eltérő érték: '16-46-47'
Sor 333: eltérő érték: '16-48-23'
Sor 334: eltérő érték: '16-49-41'
Sor 335: eltérő érték: '16-53-29'
Sor 336: eltérő érték: '16-55-35'
Sor 337: eltérő érték: '16-56-21'
Sor 338: eltérő érték: '16-58-20'
Sor 339: eltérő érték: '16-59-17'
Sor 340: eltérő érték: '17-01-00'
Sor 341: eltérő érték: '09-09-13'
Sor 342: eltérő érték: '09-51-24'
Sor 343: eltérő érték: '09-11-42'
Sor 344: eltérő érték: '09-49-59'
Sor 345: eltérő érték: '09-13-24'
Sor 346: eltérő érték: '09-49-02'
Sor 347: eltérő érték: '09-47-33'
Sor 348: eltérő érték: '09-16-01'
Sor 349: eltérő érték: '17-52-47'
Sor 350: eltérő érték: '17-54-31'
Sor 351: eltérő érték: '17-50-02'
Sor 352: eltérő érték: '15-37-01'
Sor 353: eltérő érték: '15-35-37'
Sor 354: eltérő érték: '15-34-27'
Sor 355: eltérő érték: '15-07-11'
Sor 356: eltérő érték: '15-05-25'
Sor 357: eltérő érték: '14-56-19'
Sor 358: eltérő érték: '14-52-49'
Sor 359: eltérő érték: '14-34-19'
Sor 360: eltérő érték: '15-15-55'
Sor 361: eltérő érték: '15-12-59'
Sor 362: eltérő érték: '15-11-21'
Sor 363: eltérő érték: '15-10-05'
Sor 364: eltérő érték: '15-08-26'
Sor 365: eltérő érték: '15-04-00'
Sor 366: eltérő érték: '15-02-25'
Sor 367: eltérő érték: '15-01-25'
Sor 368: eltérő érték: '14-58-58'
Sor 369: eltérő érték: '14-58-10'
Sor 370: eltérő érték: '15-32-45'
Sor 371: eltérő érték: '15-31-12'
Sor 372: eltérő érték: '15-30-06'
Sor 373: eltérő érték: '15-27-28'
Sor 374: eltérő érték: '15-27-24'
Sor 375: eltérő érték: '15-21-26'
Sor 376: eltérő érték: '15-18-45'
Sor 377: eltérő érték: '14-32-12'
Sor 378: eltérő érték: '14-31-19'
Sor 379: eltérő érték: '14-18-13'
Sor 380: eltérő érték: '14-24-03'
Sor 381: eltérő érték: '14-22-49'
Sor 382: eltérő érték: '14-21-12'
Sor 383: eltérő érték: '14-20-06'
Sor 384: eltérő érték: '10-31-30'
Sor 385: eltérő érték: '10-28-24'
Sor 386: eltérő érték: '10-24-37'
Sor 387: eltérő érték: '10-22-14'
Sor 388: eltérő érték: '10-04-22'
Sor 389: eltérő érték: '12-49-11'
Sor 390: eltérő érték: '12-47-32'
Sor 391: eltérő érték: '14-52-02'
Sor 392: eltérő érték: '14-48-19'
Sor 393: eltérő érték: '14-48-09'
Sor 394: eltérő érték: '14-38-11'
Sor 395: eltérő érték: '14-35-25'
Sor 396: eltérő érték: '10-19-32'
Sor 397: eltérő érték: '12-45-32'
Sor 398: eltérő érték: '12-44-11'
Sor 399: eltérő érték: '14-03-08'
Sor 400: eltérő érték: '12-05-03'
Sor 401: eltérő érték: '11-53-28'
Sor 402: eltérő érték: '11-50-48'
Sor 403: eltérő érték: '14-55-18'
Sor 404: eltérő érték: '10-47-08'
Sor 405: eltérő érték: '10-34-11'
Sor 406: eltérő érték: '11-41-18'
Sor 407: eltérő érték: '11-33-24'
Sor 408: eltérő érték: '14-29-20'
Sor 409: eltérő érték: '14-27-41'
Sor 410: eltérő érték: '14-16-52'
Sor 411: eltérő érték: '10-56-17'
Sor 412: eltérő érték: '10-52-19'
Sor 413: eltérő érték: '10-49-26'
Sor 414: eltérő érték: '11-48-55'
Sor 415: eltérő érték: '11-44-45'
Sor 416: eltérő érték: '11-42-50'
Sor 417: eltérő érték: '11-39-28'
Sor 418: eltérő érték: '11-37-43'
Sor 419: eltérő érték: '11-36-29'
Sor 420: eltérő érték: '14-04-45'
Sor 421: eltérő érték: '14-10-40'
Sor 422: eltérő érték: '14-06-23'
Sor 423: eltérő érték: '14-14-07'
Sor 424: eltérő érték: '14-12-14'
Sor 425: eltérő érték: '14-15-51'
Sor 426: eltérő érték: '09-43-40'
Sor 427: eltérő érték: '17-51-22'
Sor 428: eltérő érték: '14-07-54'
Sor 429: eltérő érték: '14-09-13'
Sor 430: eltérő érték: '11-06-07'
Sor 431: eltérő érték: '11-04-21'
Sor 432: eltérő érték: '11-02-08'
Sor 433: eltérő érték: '11-00-46'
Sor 434: eltérő érték: '14-01-44'
Sor 435: eltérő érték: '13-59-57'
Sor 436: eltérő érték: '11-10-28'
Sor 437: eltérő érték: '11-07-54'
Sor 438: eltérő érték: '09-46-31'
Sor 439: eltérő érték: '09-44-59'
Sor 440: eltérő érték: '09-40-23'
Sor 441: eltérő érték: '09-41-01'
Sor 442: eltérő érték: '09-05-46'
Sor 443: eltérő érték: '09-37-22'
Sor 444: eltérő érték: '09-15-06'
Sor 445: eltérő érték: '09-42-38'
Sor 446: eltérő érték: '09-37-20'
Sor 447: eltérő érték: '08-33-32'
Sor 448: eltérő érték: '08-27-15'
Sor 449: eltérő érték: '08-24-14'
Sor 450: eltérő érték: '08-22-52'
Sor 451: eltérő érték: '15-26-48'
